# -*- coding: utf-8 -*-
"""
完整分析运行脚本
用于生成论文所需的所有图表和分析结果
"""

import os
import sys
import subprocess
import time

def check_environment():
    """检查运行环境"""
    print("=== 环境检查 ===")
    
    # 检查Python版本
    python_version = sys.version
    print(f"Python版本: {python_version}")
    
    # 检查必要的包
    required_packages = [
        'tensorflow', 'keras', 'opencv-python', 'numpy', 
        'matplotlib', 'scikit-learn', 'seaborn', 'pandas'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("环境检查通过!")
    return True

def check_data_structure():
    """检查数据结构"""
    print("\n=== 数据结构检查 ===")
    
    required_dirs = [
        './inputs/train/cat',
        './inputs/train/dog', 
        './inputs/dev/cat',
        './inputs/dev/dog',
        './log'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            file_count = len([f for f in os.listdir(dir_path) 
                            if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
            print(f"✓ {dir_path} ({file_count} 张图片)")
        else:
            print(f"✗ {dir_path} (不存在)")
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"\n缺失的目录: {missing_dirs}")
        print("请确保数据集已正确组织")
        return False
    
    print("数据结构检查通过!")
    return True

def run_data_analysis():
    """运行数据分析"""
    print("\n=== 运行数据分析 ===")
    try:
        from data_analysis_visualization import DataAnalysisVisualizer
        
        visualizer = DataAnalysisVisualizer()
        
        print("1. 分析数据集统计信息...")
        visualizer.analyze_dataset_statistics()
        
        print("2. 展示样本图像...")
        visualizer.show_sample_images()
        
        print("3. 展示预处理效果...")
        visualizer.show_preprocessing_comparison()
        
        print("4. 展示数据增强效果...")
        visualizer.show_data_augmentation()
        
        print("5. 绘制训练历史曲线...")
        visualizer.plot_training_history()
        
        print("6. 绘制混淆矩阵...")
        visualizer.plot_confusion_matrix()
        
        print("7. 绘制模型对比图...")
        visualizer.plot_model_comparison()
        
        print("数据分析完成!")
        return True
        
    except Exception as e:
        print(f"数据分析失败: {e}")
        return False

def run_model_training():
    """运行模型训练"""
    print("\n=== 模型训练选择 ===")
    print("1. VGG16迁移学习 (推荐)")
    print("2. 自定义CNN")
    print("3. 跳过训练")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == '1':
        print("\n开始VGG16迁移学习训练...")
        try:
            # 运行VGG16训练
            result = subprocess.run([
                sys.executable, 'pre_train.py', 
                '--num_epochs=5', '--batch_size=16'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("VGG16训练完成!")
                return True
            else:
                print(f"VGG16训练失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"VGG16训练异常: {e}")
            return False
            
    elif choice == '2':
        print("\n开始自定义CNN训练...")
        try:
            # 运行自定义CNN训练
            result = subprocess.run([
                sys.executable, 'train_simple.py',
                '--num_epochs=3', '--batch_size=16'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("自定义CNN训练完成!")
                return True
            else:
                print(f"自定义CNN训练失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"自定义CNN训练异常: {e}")
            return False
            
    elif choice == '3':
        print("跳过模型训练")
        return True
    else:
        print("无效选择")
        return False

def run_model_evaluation():
    """运行模型评估"""
    print("\n=== 运行模型评估 ===")
    
    # 检查是否存在训练好的模型
    model_path = './log/VGG16-transfer-learning.h5'
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        print("请先训练模型")
        return False
    
    try:
        from model_evaluation import ModelEvaluator
        
        evaluator = ModelEvaluator()
        
        print("1. 加载模型...")
        if not evaluator.load_model():
            return False
        
        print("2. 分析模型架构...")
        evaluator.analyze_model_performance()
        
        print("3. 在测试集上评估...")
        evaluator.evaluate_on_test_set()
        
        print("4. 预测样本图像...")
        evaluator.predict_sample_images()
        
        print("5. 生成模型对比...")
        evaluator.compare_with_baseline()
        
        print("模型评估完成!")
        return True
        
    except Exception as e:
        print(f"模型评估失败: {e}")
        return False

def generate_report():
    """生成分析报告"""
    print("\n=== 生成分析报告 ===")
    
    # 统计生成的文件
    generated_files = []
    
    # 数据分析图表
    analysis_files = [
        'dataset_distribution.png',
        'sample_images.png',
        'preprocessing_comparison.png', 
        'data_augmentation.png',
        'training_history.png',
        'confusion_matrix.png',
        'model_comparison.png'
    ]
    
    # 训练和评估图表
    training_files = [
        './log/vgg16_training_history.png',
        './log/confusion_matrix.png',
        './log/prediction_results.png',
        './log/model_comparison.png'
    ]
    
    print("生成的文件列表:")
    print("=" * 50)
    
    print("\n数据分析图表:")
    for file in analysis_files:
        if os.path.exists(file):
            print(f"✓ {file}")
            generated_files.append(file)
        else:
            print(f"✗ {file}")
    
    print("\n训练和评估图表:")
    for file in training_files:
        if os.path.exists(file):
            print(f"✓ {file}")
            generated_files.append(file)
        else:
            print(f"✗ {file}")
    
    print(f"\n总计生成 {len(generated_files)} 个文件")
    
    # 生成使用说明
    usage_guide = """
=== 论文图表使用指南 ===

1. 数据集分析部分:
   - dataset_distribution.png: 数据集分布统计图
   - sample_images.png: 原始数据样本展示

2. 数据预处理部分:
   - preprocessing_comparison.png: 图像预处理前后对比
   - data_augmentation.png: 数据增强效果展示

3. 模型训练部分:
   - vgg16_training_history.png: 训练损失和准确率曲线

4. 模型评估部分:
   - confusion_matrix.png: 混淆矩阵
   - prediction_results.png: 模型预测结果展示
   - model_comparison.png: 不同模型性能对比

请将这些图片插入到论文的相应位置。
"""
    
    print(usage_guide)
    
    # 保存使用指南
    with open('图表使用指南.txt', 'w', encoding='utf-8') as f:
        f.write(usage_guide)
    
    print("使用指南已保存到: 图表使用指南.txt")

def main():
    """主函数"""
    print("=" * 60)
    print("CNN猫狗图像分类 - 完整分析程序")
    print("=" * 60)
    
    # 1. 环境检查
    if not check_environment():
        print("环境检查失败，程序退出")
        return
    
    # 2. 数据结构检查
    if not check_data_structure():
        print("数据结构检查失败，程序退出")
        return
    
    # 3. 运行数据分析
    print("\n是否运行数据分析? (y/n): ", end="")
    if input().lower() == 'y':
        run_data_analysis()
    
    # 4. 运行模型训练
    print("\n是否运行模型训练? (y/n): ", end="")
    if input().lower() == 'y':
        run_model_training()
    
    # 5. 运行模型评估
    print("\n是否运行模型评估? (y/n): ", end="")
    if input().lower() == 'y':
        run_model_evaluation()
    
    # 6. 生成报告
    generate_report()
    
    print("\n" + "=" * 60)
    print("完整分析程序执行完成!")
    print("请查看生成的图表文件和论文模板")
    print("=" * 60)

if __name__ == "__main__":
    main()
