# -*- coding: utf-8 -*-
"""
模型评估和预测脚本
用于评估训练好的模型性能并生成预测结果
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import cv2
import keras
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns
import random
from tensorflow.keras.preprocessing.image import ImageDataGenerator

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ModelEvaluator:
    """模型评估类"""
    
    def __init__(self, model_path='./log/VGG16-transfer-learning.h5', 
                 test_data_dir='./inputs/dev'):
        self.model_path = model_path
        self.test_data_dir = test_data_dir
        self.model = None
        self.class_names = ['cat', 'dog']
        
    def load_model(self):
        """加载训练好的模型"""
        if os.path.exists(self.model_path):
            print(f"正在加载模型: {self.model_path}")
            self.model = keras.models.load_model(self.model_path)
            print("模型加载成功!")
            return True
        else:
            print(f"模型文件不存在: {self.model_path}")
            return False
    
    def evaluate_on_test_set(self):
        """在测试集上评估模型"""
        if self.model is None:
            print("请先加载模型!")
            return
        
        # 创建测试数据生成器
        test_datagen = ImageDataGenerator(rescale=1./255)
        test_generator = test_datagen.flow_from_directory(
            self.test_data_dir,
            target_size=(224, 224),
            batch_size=32,
            class_mode='categorical',
            shuffle=False)
        
        print("正在评估模型...")
        
        # 评估模型
        test_loss, test_accuracy = self.model.evaluate(test_generator, verbose=1)
        
        print(f"\n=== 测试集评估结果 ===")
        print(f"测试损失: {test_loss:.4f}")
        print(f"测试准确率: {test_accuracy:.4f}")
        
        # 获取预测结果
        predictions = self.model.predict(test_generator, verbose=1)
        predicted_classes = np.argmax(predictions, axis=1)
        true_classes = test_generator.classes
        
        # 生成分类报告
        print("\n=== 详细分类报告 ===")
        report = classification_report(true_classes, predicted_classes, 
                                     target_names=self.class_names)
        print(report)
        
        # 绘制混淆矩阵
        self.plot_confusion_matrix(true_classes, predicted_classes)
        
        return test_accuracy, predictions, true_classes, predicted_classes
    
    def plot_confusion_matrix(self, y_true, y_pred):
        """绘制混淆矩阵"""
        cm = confusion_matrix(y_true, y_pred)
        
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['猫', '狗'], yticklabels=['猫', '狗'])
        plt.title('混淆矩阵')
        plt.xlabel('预测类别')
        plt.ylabel('真实类别')
        
        # 添加准确率信息
        accuracy = np.trace(cm) / np.sum(cm)
        plt.figtext(0.15, 0.02, f'总体准确率: {accuracy:.4f}', fontsize=12)
        
        plt.tight_layout()
        plt.savefig('./log/confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("混淆矩阵已保存到: ./log/confusion_matrix.png")
    
    def predict_sample_images(self, num_samples=8):
        """对样本图像进行预测并可视化结果"""
        if self.model is None:
            print("请先加载模型!")
            return
        
        # 收集测试图像
        test_images = []
        test_labels = []
        test_paths = []
        
        for class_idx, class_name in enumerate(self.class_names):
            class_dir = os.path.join(self.test_data_dir, class_name)
            if os.path.exists(class_dir):
                images = [f for f in os.listdir(class_dir) 
                         if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                
                # 随机选择样本
                selected_images = random.sample(images, min(num_samples//2, len(images)))
                
                for img_name in selected_images:
                    img_path = os.path.join(class_dir, img_name)
                    test_paths.append(img_path)
                    test_labels.append(class_idx)
        
        # 预处理图像并进行预测
        predictions = []
        processed_images = []
        
        for img_path in test_paths:
            # 加载和预处理图像
            img = cv2.imread(img_path)
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img_resized = cv2.resize(img, (224, 224))
            img_normalized = img_resized / 255.0
            img_batch = np.expand_dims(img_normalized, axis=0)
            
            # 预测
            pred = self.model.predict(img_batch, verbose=0)
            predictions.append(pred[0])
            processed_images.append(img_resized)
        
        # 可视化预测结果
        self.visualize_predictions(processed_images, test_labels, predictions, test_paths)
    
    def visualize_predictions(self, images, true_labels, predictions, image_paths):
        """可视化预测结果"""
        num_images = len(images)
        cols = 4
        rows = (num_images + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(15, 4*rows))
        if rows == 1:
            axes = axes.reshape(1, -1)
        
        for i in range(num_images):
            row = i // cols
            col = i % cols
            
            # 预测结果
            pred_class = np.argmax(predictions[i])
            pred_prob = np.max(predictions[i])
            true_class = true_labels[i]
            
            # 确定预测是否正确
            is_correct = pred_class == true_class
            color = 'green' if is_correct else 'red'
            
            # 显示图像
            axes[row, col].imshow(images[i])
            
            # 设置标题
            title = f"真实: {self.class_names[true_class]}\n"
            title += f"预测: {self.class_names[pred_class]} ({pred_prob:.2f})"
            axes[row, col].set_title(title, color=color, fontsize=10)
            axes[row, col].axis('off')
            
            # 添加边框
            for spine in axes[row, col].spines.values():
                spine.set_edgecolor(color)
                spine.set_linewidth(2)
        
        # 隐藏多余的子图
        for i in range(num_images, rows * cols):
            row = i // cols
            col = i % cols
            axes[row, col].axis('off')
        
        plt.suptitle('模型预测结果展示', fontsize=16)
        plt.tight_layout()
        plt.savefig('./log/prediction_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("预测结果图已保存到: ./log/prediction_results.png")
    
    def analyze_model_performance(self):
        """分析模型性能"""
        if self.model is None:
            print("请先加载模型!")
            return
        
        print("\n=== 模型架构分析 ===")
        self.model.summary()
        
        # 计算模型参数
        total_params = self.model.count_params()
        trainable_params = sum([keras.backend.count_params(w) for w in self.model.trainable_weights])
        non_trainable_params = total_params - trainable_params
        
        print(f"\n=== 模型参数统计 ===")
        print(f"总参数数量: {total_params:,}")
        print(f"可训练参数: {trainable_params:,}")
        print(f"不可训练参数: {non_trainable_params:,}")
        
        # 模型大小估算
        model_size_mb = total_params * 4 / (1024 * 1024)  # 假设每个参数4字节
        print(f"估算模型大小: {model_size_mb:.2f} MB")
    
    def compare_with_baseline(self):
        """与基线模型对比"""
        print("\n=== 模型性能对比 ===")
        
        # 模拟基线结果（实际应该从训练日志中获取）
        baseline_results = {
            '随机猜测': 0.5000,
            '自定义CNN': 0.8300,
            'VGG16迁移学习': 1.0000
        }
        
        models = list(baseline_results.keys())
        accuracies = list(baseline_results.values())
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(models, accuracies, color=['red', 'orange', 'green'], alpha=0.7)
        
        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{acc:.3f}', ha='center', va='bottom', fontsize=12)
        
        plt.title('不同模型准确率对比')
        plt.ylabel('准确率')
        plt.ylim(0, 1.1)
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('./log/model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("模型对比图已保存到: ./log/model_comparison.png")

def main():
    """主函数"""
    print("=== 模型评估程序 ===")
    
    evaluator = ModelEvaluator()
    
    # 加载模型
    if not evaluator.load_model():
        print("无法加载模型，程序退出")
        return
    
    # 分析模型架构
    evaluator.analyze_model_performance()
    
    # 在测试集上评估
    print("\n开始在测试集上评估模型...")
    evaluator.evaluate_on_test_set()
    
    # 预测样本图像
    print("\n开始预测样本图像...")
    evaluator.predict_sample_images()
    
    # 模型对比
    print("\n生成模型对比图...")
    evaluator.compare_with_baseline()
    
    print("\n=== 评估完成 ===")
    print("生成的文件:")
    print("- ./log/confusion_matrix.png")
    print("- ./log/prediction_results.png") 
    print("- ./log/model_comparison.png")

if __name__ == "__main__":
    main()
