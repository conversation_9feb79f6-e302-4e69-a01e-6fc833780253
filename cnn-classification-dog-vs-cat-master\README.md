# 基于CNN的猫狗图像分类项目 - 非结构化数据挖掘期末作业

本项目实现了基于卷积神经网络（CNN）的猫狗图像分类，使用Kaggle的Dogs vs. Cats数据集。项目包含两种不同的实现方法：自定义CNN模型和基于VGG16的迁移学习。

## 🎯 项目目标

本项目作为非结构化数据挖掘课程的期末作业，完整展示了图像分类任务的全流程：
- 数据预处理和探索性分析
- 深度学习模型构建和训练
- 模型评估和性能分析
- 结果可视化和论文撰写

## 📊 项目成果

- ✅ **高精度分类**：VGG16迁移学习模型验证准确率达到100%
- ✅ **完整流程**：从数据预处理到模型部署的完整实现
- ✅ **详细分析**：包含数据分析、模型对比、性能评估
- ✅ **学术论文**：符合课程要求的完整论文
- ✅ **可视化图表**：丰富的图表支持论文内容

## 1. 环境要求

### 基础环境
- Python 3.6+ (推荐Python 3.7-3.11)
- Anaconda (推荐)

### 依赖库
- numpy >= 1.14.2 (数值计算)
- keras >= 2.1.6 (深度学习高级API)
- tensorflow >= 1.6.0 (深度学习框架)
- h5py >= 2.7.0 (HDF5文件处理)
- python-gflags >= 3.1.2 (命令行参数解析)
- opencv-python >= ******** (计算机视觉库)
- matplotlib >= 3.3.0 (绘图库)
- scikit-learn >= 0.24.0 (机器学习库)

### 快速安装
```bash
# 使用pip安装所有依赖
pip install tensorflow keras opencv-python numpy matplotlib scikit-learn h5py python-gflags

# 或使用requirements.txt
pip install -r requirements.txt
```

## 2. 🚀 快速开始 - 期末作业完整流程

### 方法一：一键运行完整分析（推荐）

```bash
# 运行完整分析程序
python run_complete_analysis.py
```

这个脚本会自动：
1. ✅ 检查环境和数据结构
2. 📊 生成数据分析图表
3. 🤖 训练模型（可选）
4. 📈 评估模型性能
5. 🖼️ 生成论文所需的所有图表

### 方法二：生成数据分析图表

```bash
# 生成论文所需的所有图表
python data_analysis_visualization.py
```

生成的图表包括：
- `dataset_distribution.png` - 数据集分布统计
- `sample_images.png` - 样本图像展示
- `preprocessing_comparison.png` - 预处理效果对比
- `data_augmentation.png` - 数据增强效果
- `training_history.png` - 训练历史曲线
- `confusion_matrix.png` - 混淆矩阵
- `model_comparison.png` - 模型性能对比

### 方法三：模型训练和评估

```bash
# VGG16迁移学习训练（推荐）
python pre_train.py --num_epochs=5 --batch_size=16

# 模型评估
python model_evaluation.py
```

## 3. 文件说明

### 新增文件（期末作业专用）
- **run_complete_analysis.py**: 一键运行完整分析的主脚本
- **data_analysis_visualization.py**: 数据分析和可视化脚本
- **model_evaluation.py**: 模型评估和预测脚本
- **基于CNN的猫狗图像分类研究论文.md**: 完整的学术论文

### 原有核心文件

### 核心文件
- **train.py**: 自定义CNN模型训练脚本
  - 实现轻量级的3层卷积神经网络
  - 训练后测试集准确率约83%
  - 适合资源受限环境

- **pre_train.py**: VGG16迁移学习训练脚本
  - 基于ImageNet预训练的VGG16网络
  - 采用迁移学习技术，准确率可达95%以上
  - 训练速度快，效果好

- **img_cnn.py**: 自定义CNN网络架构定义
  - 包含3个卷积层和2个全连接层
  - 使用ReLU激活函数和Dropout正则化
  - 基于TensorFlow实现

- **data_helper.py**: 数据预处理和加载模块
  - 图像尺寸调整和归一化
  - RGB到灰度转换
  - 批处理数据生成器

### 数据目录
- **inputs/**: 猫狗图片样本数据目录
  - 数据来源：[Kaggle Dogs vs. Cats](https://www.kaggle.com/c/dogs-vs-cats/data)
  - 需要按以下结构组织：
  ```
  inputs/
  ├── train/
  │   ├── cat/
  │   │   ├── cat.0.jpg
  │   │   ├── cat.1.jpg
  │   │   └── ...
  │   └── dog/
  │       ├── dog.0.jpg
  │       ├── dog.1.jpg
  │       └── ...
  └── dev/
      ├── cat/
      └── dog/
  ```

## 3. 模型训练

### 3.1 训练自定义CNN模型
```bash
python train.py
```

**训练参数说明：**
- 图像尺寸：224×224×1（灰度图）
- 批次大小：32
- 训练轮数：200
- 学习率：0.001
- 优化器：Adam

**预期结果：**
- 训练时间：约2小时（CPU）
- 最终准确率：约83%
- 模型大小：约2MB

### 3.2 训练VGG16迁移学习模型
```bash
python pre_train.py
```

**训练参数说明：**
- 图像尺寸：224×224×3（RGB图）
- 批次大小：32
- 训练轮数：10-50
- 学习率：0.001
- 优化器：SGD with momentum

**预期结果：**
- 训练时间：约1小时（GPU推荐）
- 最终准确率：约95%+
- 模型大小：约60MB

### 3.3 自定义训练参数
可以通过命令行参数自定义训练配置：

```bash
# 自定义CNN训练示例
python train.py --batch_size 64 --num_epochs 100 --learning_rate 0.0001

# VGG16迁移学习训练示例
python pre_train.py --batch_size 16 --num_epochs 20 --learning_rate 0.001
```

## 4. 结果可视化

### 4.1 TensorBoard可视化
```bash
# 启动TensorBoard
tensorboard --logdir ./log/

# 在浏览器中访问
http://localhost:6006
```

TensorBoard可以显示：
- 训练和验证损失曲线
- 准确率变化趋势
- 网络结构图
- 权重分布直方图

### 4.2 训练结果对比

| 模型类型 | 准确率 | 训练时间 | 模型大小 | 收敛轮数 | 适用场景 |
|---------|--------|----------|----------|----------|----------|
| 自定义CNN | 83% | 2小时 | 2MB | 50轮 | 资源受限环境 |
| VGG16迁移学习 | 95%+ | 1小时 | 60MB | 20轮 | 高精度要求 |

## 5. 模型架构详解

### 5.1 自定义CNN架构
```
输入层: 224×224×1
    ↓
卷积层1: 5×5×8 + ReLU + 2×2池化 → 112×112×8
    ↓
卷积层2: 5×5×16 + ReLU + 2×2池化 → 56×56×16
    ↓
卷积层3: 3×3×32 + ReLU + 2×2池化 → 28×28×32
    ↓
展平层: 25088个特征
    ↓
全连接层1: 128个神经元 + ReLU
    ↓
Dropout层: 保留概率0.7
    ↓
输出层: 2个神经元（猫/狗分类）
```

### 5.2 VGG16迁移学习架构
```
输入层: 224×224×3
    ↓
VGG16预训练层（冻结）: 特征提取
    ↓
全局平均池化层: 降维
    ↓
全连接层: 128个神经元 + ReLU
    ↓
Dropout层: 保留概率0.5
    ↓
输出层: 2个神经元 + Softmax
```

## 6. 使用技巧

### 6.1 数据准备建议
1. **图像质量**：确保图像清晰，避免过度模糊
2. **数据平衡**：保持猫狗样本数量基本相等
3. **文件格式**：支持JPEG、PNG等常见格式
4. **命名规范**：文件名包含类别信息（如cat.xxx.jpg）

### 6.2 训练优化建议
1. **GPU加速**：VGG16模型建议使用GPU训练
2. **内存管理**：如遇内存不足，可减小batch_size
3. **早停机制**：监控验证损失，避免过拟合
4. **学习率调整**：可尝试学习率衰减策略

### 6.3 常见问题解决
1. **CUDA错误**：检查GPU驱动和CUDA版本兼容性
2. **内存溢出**：减小批次大小或使用CPU训练
3. **收敛缓慢**：调整学习率或使用预训练模型
4. **准确率低**：检查数据质量和标签正确性

## 7. 扩展功能

### 7.1 模型改进方向
- 尝试其他预训练模型（ResNet、DenseNet等）
- 实现数据增强技术（旋转、翻转、缩放等）
- 添加注意力机制
- 使用集成学习方法

### 7.2 应用扩展
- 支持更多动物类别分类
- 开发Web应用界面
- 移动端模型部署
- 实时视频分类

## 8. 参考资料

### 8.1 数据集来源
- [Kaggle Dogs vs. Cats Competition](https://www.kaggle.com/c/dogs-vs-cats/data)

### 8.2 技术文档
- [Keras Applications](https://keras.io/applications/) - 预训练模型使用
- [Keras Image Preprocessing](https://keras.io/preprocessing/image/) - 图像预处理
- [TensorFlow官方文档](https://tensorflow.org/) - 深度学习框架
- [OpenCV Python教程](https://opencv-python-tutroals.readthedocs.io/) - 计算机视觉

### 8.3 相关论文
- [ImageNet Classification with Deep Convolutional Neural Networks](https://papers.nips.cc/paper/4824-imagenet-classification-with-deep-convolutional-neural-networks.pdf) - AlexNet
- [Very Deep Convolutional Networks for Large-Scale Image Recognition](https://arxiv.org/abs/1409.1556) - VGG

## 9. 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 10. 贡献

欢迎提交Issue和Pull Request来改进项目！

---

**项目作者**: 基于CNN的猫狗图像分类研究团队
**最后更新**: 2024年
**项目状态**: 活跃维护中