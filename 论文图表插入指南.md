# 📊 论文图表插入指南

## 🎯 图表与论文章节对应关系

根据生成的图表文件，请按以下顺序将图片插入到论文的相应位置：

### 第二章 数据预处理

#### 2.1 数据分析

**【图片插入位置1】**：
- **文件名**：`dataset_distribution.png`
- **图片说明**：数据集分布统计图
- **插入位置**：论文第2.1节"数据分析"部分
- **图片标题**：图2-1 数据集分布统计图

**【图片插入位置2】**：
- **文件名**：`sample_images.png`
- **图片说明**：样本图像展示
- **插入位置**：论文第2.1节"数据分析"部分
- **图片标题**：图2-2 数据集样本图像展示

#### 2.2 归一化处理

**【图片插入位置3】**：
- **文件名**：`preprocessing_comparison.png`
- **图片说明**：图像预处理前后对比
- **插入位置**：论文第2.2节"归一化处理"部分
- **图片标题**：图2-3 图像预处理效果对比

#### 2.3 数据增强策略

**【图片插入位置4】**：
- **文件名**：`data_augmentation.png`
- **图片说明**：数据增强效果展示
- **插入位置**：论文第2.3节"数据增强策略"部分
- **图片标题**：图2-4 数据增强效果展示

### 第四章 模型评估

#### 4.1 模型训练结果

**【图片插入位置5】**：
- **文件名**：`training_history.png`
- **图片说明**：训练历史曲线（损失和准确率）
- **插入位置**：论文第4.1节"模型训练结果"部分
- **图片标题**：图4-1 模型训练历史曲线

#### 4.2 关键指标分析

**【图片插入位置6】**：
- **文件名**：`confusion_matrix.png`
- **图片说明**：混淆矩阵
- **插入位置**：论文第4.2节"关键指标分析"部分
- **图片标题**：图4-2 模型预测混淆矩阵

**【图片插入位置7】**：
- **文件名**：`model_comparison.png`
- **图片说明**：模型性能对比图
- **插入位置**：论文第4.2节"关键指标分析"部分
- **图片标题**：图4-3 不同模型性能对比

## 📝 图片插入格式建议

在Markdown文档中，建议使用以下格式插入图片：

```markdown
![图2-1 数据集分布统计图](dataset_distribution.png)

图2-1 数据集分布统计图
```

或者在Word文档中：
1. 插入图片
2. 添加图片标题
3. 设置图片居中对齐
4. 调整图片大小（建议宽度不超过页面的80%）

## 🔍 图表内容说明

### 1. dataset_distribution.png
- **内容**：展示训练集和验证集中猫狗图片的数量分布
- **数据**：训练集各400张，验证集各100张，总计1000张图片
- **用途**：证明数据集的平衡性和规模合理性

### 2. sample_images.png
- **内容**：展示数据集中的典型猫狗图像样本
- **特点**：包含不同品种、姿态、背景的图像
- **用途**：直观展示数据集的多样性和复杂性

### 3. preprocessing_comparison.png
- **内容**：对比原始图像和预处理后的效果
- **步骤**：尺寸调整(224×224)、归一化、灰度转换
- **用途**：说明数据预处理的必要性和效果

### 4. data_augmentation.png
- **内容**：展示数据增强技术的效果
- **技术**：旋转、翻转、缩放、平移等变换
- **用途**：证明数据增强对提升模型泛化能力的作用

### 5. training_history.png
- **内容**：训练过程中损失函数和准确率的变化曲线
- **数据**：VGG16模型5轮训练的完整记录
- **用途**：展示模型收敛过程和训练效果

### 6. confusion_matrix.png
- **内容**：模型预测结果的混淆矩阵
- **结果**：完美分类，无误分类样本
- **用途**：量化评估模型的分类性能

### 7. model_comparison.png
- **内容**：自定义CNN与VGG16迁移学习的性能对比
- **指标**：准确率、训练时间、模型大小等
- **用途**：对比分析不同方法的优劣

## ✅ 检查清单

在提交论文前，请确认：

- [ ] 所有7个图表都已正确插入
- [ ] 图片标题格式统一
- [ ] 图片清晰度良好
- [ ] 图片与文字内容对应
- [ ] 图片编号连续正确
- [ ] 图片大小适中，不影响阅读

## 📋 提交文件清单

最终提交应包含：

1. **论文文件**：`基于CNN的猫狗图像分类研究论文.md`（已插入图片）
2. **图表文件**：7个PNG格式图片
3. **源代码**：`cnn-classification-dog-vs-cat-master/`文件夹
4. **说明文档**：本指南和运行指南

按"班级+学号+姓名"格式命名后压缩提交。

---

**注意**：请在论文中填写您的个人信息（姓名、学号等），确保内容完整性！
